SELECT
    (SELECT can_engine_motorhours 
     FROM ssr_can_bus_sensor_data 
     WHERE asset_id = 'ass_fef1baf7-833c-42ed-b7cd-45983f10d70c'
       AND client_id = 'cli_00000000-0000-0000-0000-000000000000' 
       AND can_engine_motorhours IS NOT NULL 
     ORDER BY time DESC 
     LIMIT 1) AS can_engine_motorhours,
    
    (SELECT can_vehicle_mileage 
     FROM ssr_can_bus_sensor_data 
     WHERE asset_id = 'ass_fef1baf7-833c-42ed-b7cd-45983f10d70c' 
       AND client_id = 'cli_00000000-0000-0000-0000-000000000000'
       AND can_vehicle_mileage IS NOT NULL 
     ORDER BY time DESC 
     LIMIT 1) AS can_vehicle_mileage,
    
    (SELECT can_fuel_consumed 
     FROM ssr_can_bus_sensor_data 
     WHERE asset_id = 'ass_fef1baf7-833c-42ed-b7cd-45983f10d70c' 
       AND client_id = 'cli_00000000-0000-0000-0000-000000000000'
       AND can_fuel_consumed IS NOT NULL 
     ORDER BY time DESC 
     LIMIT 1) AS can_fuel_consumed;