package handler

import (
	"assetfindr/internal/app/asset/dtos"
	"assetfindr/internal/app/asset/usecase"
	"assetfindr/internal/errorhandler"
	"net/http"

	"github.com/gin-gonic/gin"
)

type HaulHandler struct {
	HaulUseCase *usecase.HaulUseCase
}

func NewHaulHandler(
	haulUseCase *usecase.HaulUseCase,
) *HaulHandler {
	return &HaulHandler{
		HaulUseCase: haulUseCase,
	}
}

func (h *<PERSON><PERSON><PERSON>andler) DriverCheckIn(c *gin.Context) {
	ctx := c.Request.Context()
	imei := c.Param("imei")

	var req dtos.DriverCheckIn
	err := c.ShouldBindJSON(&req)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	resp, err := h.HaulUseCase.DriverCheckIn(ctx, imei, req)
	if err != nil {
		httpStatus, errMessage := errorhandler.ParseToHttpError(err)
		c.<PERSON>(httpStatus, gin.H{"error": errMessage})
		return
	}

	c.J<PERSON>(http.StatusOK, resp)
}

func (h *HaulHandler) DriverCheckOut(c *gin.Context) {
	ctx := c.Request.Context()
	imei := c.Param("imei")

	resp, err := h.HaulUseCase.DriverCheckOut(ctx, imei)
	if err != nil {
		httpStatus, errMessage := errorhandler.ParseToHttpError(err)
		c.JSON(httpStatus, gin.H{"error": errMessage})
		return
	}

	c.JSON(http.StatusOK, resp)
}

func (h *HaulHandler) GetDriverLoginSessionList(c *gin.Context) {
	ctx := c.Request.Context()

	var req dtos.GetDriverLoginSessionListReq
	err := c.BindQuery(&req)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	req.Normalize()
	resp, err := h.HaulUseCase.GetDriverLoginSessionList(ctx, req)
	if err != nil {
		httpStatus, errMessage := errorhandler.ParseToHttpError(err)
		c.JSON(httpStatus, gin.H{"error": errMessage})
		return
	}

	c.JSON(http.StatusOK, resp)
}

func (h *HaulHandler) GetCurrentDriverLoginSession(c *gin.Context) {
	ctx := c.Request.Context()
	imei := c.Param("imei")

	resp, err := h.HaulUseCase.GetCurrentDriverLoginSession(ctx, imei)
	if err != nil {
		httpStatus, errMessage := errorhandler.ParseToHttpError(err)
		c.JSON(httpStatus, gin.H{"error": errMessage})
		return
	}

	c.JSON(http.StatusOK, resp)
}

func (h *HaulHandler) GetHaulStatuses(c *gin.Context) {
	ctx := c.Request.Context()

	resp, err := h.HaulUseCase.GetHaulStatuses(ctx)
	if err != nil {
		httpStatus, errMessage := errorhandler.ParseToHttpError(err)
		c.JSON(httpStatus, gin.H{"error": errMessage})
		return
	}

	c.JSON(http.StatusOK, resp)
}

func (h *HaulHandler) GetHaulActivities(c *gin.Context) {
	ctx := c.Request.Context()
	req := dtos.HaulActivityListReq{}
	err := c.BindQuery(&req)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	resp, err := h.HaulUseCase.GetHaulActivities(ctx, req)
	if err != nil {
		httpStatus, errMessage := errorhandler.ParseToHttpError(err)
		c.JSON(httpStatus, gin.H{"error": errMessage})
		return
	}

	c.JSON(http.StatusOK, resp)
}

func (h *HaulHandler) GetHaulSubActivities(c *gin.Context) {
	ctx := c.Request.Context()
	req := dtos.HaulSubActivityListReq{}
	err := c.BindQuery(&req)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	resp, err := h.HaulUseCase.GetHaulSubActivities(ctx, req)
	if err != nil {
		httpStatus, errMessage := errorhandler.ParseToHttpError(err)
		c.JSON(httpStatus, gin.H{"error": errMessage})
		return
	}

	c.JSON(http.StatusOK, resp)
}

func (h *HaulHandler) GetCurrentHauling(c *gin.Context) {
	ctx := c.Request.Context()
	imei := c.Param("imei")

	resp, err := h.HaulUseCase.GetCurrentHauling(ctx, imei)
	if err != nil {
		httpStatus, errMessage := errorhandler.ParseToHttpError(err)
		c.JSON(httpStatus, gin.H{"error": errMessage})
		return
	}

	c.JSON(http.StatusOK, resp)
}

func (h *HaulHandler) GetCurrentAssetHauling(c *gin.Context) {
	ctx := c.Request.Context()
	assetID := c.Param("asset_id")

	resp, err := h.HaulUseCase.GetCurrentAssetHauling(ctx, assetID)
	if err != nil {
		httpStatus, errMessage := errorhandler.ParseToHttpError(err)
		c.JSON(httpStatus, gin.H{"error": errMessage})
		return
	}

	c.JSON(http.StatusOK, resp)
}

func (h *HaulHandler) UpdateAssetHaulingStatus(c *gin.Context) {
	ctx := c.Request.Context()
	assetID := c.Param("asset_id")

	var req dtos.AssetHaulingUpdateStatusReq
	err := c.ShouldBindJSON(&req)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	resp, err := h.HaulUseCase.UpdateAssetHaulingStatus(ctx, assetID, req)
	if err != nil {
		httpStatus, errMessage := errorhandler.ParseToHttpError(err)
		c.JSON(httpStatus, gin.H{"error": errMessage})
		return
	}

	c.JSON(http.StatusOK, resp)
}

func (h *HaulHandler) GetPublicCurrentHauling(c *gin.Context) {
	ctx := c.Request.Context()
	imei := c.Param("imei")

	resp, err := h.HaulUseCase.GetPublicCurrentHauling(ctx, imei)
	if err != nil {
		httpStatus, errMessage := errorhandler.ParseToHttpError(err)
		c.JSON(httpStatus, gin.H{"error": errMessage})
		return
	}

	c.JSON(http.StatusOK, resp)
}

func (h *HaulHandler) UpdateHaulingStatus(c *gin.Context) {
	ctx := c.Request.Context()
	imei := c.Param("imei")

	var req dtos.HaulingUpdateStatusReq
	err := c.ShouldBindJSON(&req)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	err = req.Validate()
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	resp, err := h.HaulUseCase.UpdateHaulingStatus(ctx, imei, req)
	if err != nil {
		httpStatus, errMessage := errorhandler.ParseToHttpError(err)
		c.JSON(httpStatus, gin.H{"error": errMessage})
		return
	}

	c.JSON(http.StatusOK, resp)
}

func (h *HaulHandler) GetHaulingStatusBoard(c *gin.Context) {
	ctx := c.Request.Context()

	var req dtos.GetHaulingStatusBoardReq
	err := c.BindQuery(&req)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	resp, err := h.HaulUseCase.GetHaulingStatusBoard(ctx, req)
	if err != nil {
		httpStatus, errMessage := errorhandler.ParseToHttpError(err)
		c.JSON(httpStatus, gin.H{"error": errMessage})
		return
	}

	c.JSON(http.StatusOK, resp)
}

func (h *HaulHandler) GetHaulDispatchLocation(c *gin.Context) {
	ctx := c.Request.Context()
	assetID := c.Param("asset_id")

	resp, err := h.HaulUseCase.GetHaulDispatchLocation(ctx, assetID)
	if err != nil {
		httpStatus, errMessage := errorhandler.ParseToHttpError(err)
		c.JSON(httpStatus, gin.H{"error": errMessage})
		return
	}

	c.JSON(http.StatusOK, resp)
}

func (h *HaulHandler) UpsertHaulDispatchLocation(c *gin.Context) {
	ctx := c.Request.Context()
	assetID := c.Param("asset_id")

	var req dtos.UpsertHaulDispatchLocationReq
	err := c.ShouldBindJSON(&req)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	resp, err := h.HaulUseCase.UpsertHaulDispatchLocation(ctx, assetID, req)
	if err != nil {
		httpStatus, errMessage := errorhandler.ParseToHttpError(err)
		c.JSON(httpStatus, gin.H{"error": errMessage})
		return
	}

	c.JSON(http.StatusOK, resp)
}
