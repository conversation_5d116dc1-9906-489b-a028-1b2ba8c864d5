package routers

import (
	"assetfindr/internal/app/asset/handler"
	"assetfindr/pkg/middleware"

	"github.com/gin-gonic/gin"
)

func RegisterHaulRoutes(route *gin.Engine, haulHandler *handler.HaulHandler) *gin.Engine {

	haulWithDevicePublicRoutes := route.Group("/v1/hauls/devices/:imei")
	{
		haulWithDevicePublicRoutes.POST("/driver-sessions/check-in", haulHandler.DriverCheckIn)
		haulWithDevicePublicRoutes.GET("/info", haulHandler.GetPublicCurrentHauling)
	}

	haulWithDeviceRoutes := route.Group("/v1/hauls/devices/:imei", middleware.TokenValidationMiddleware())
	{
		haulWithDeviceRoutes.POST("/driver-sessions/check-out", haulHandler.DriverCheckOut)
		haulWithDeviceRoutes.GET("/driver-sessions/current", haulHandler.GetCurrentDriverLoginSession)
		haulWithDeviceRoutes.GET("/current", haulHandler.GetCurrentHauling)
		haulWithDeviceRoutes.POST("", haulHandler.UpdateHaulingStatus)
	}

	haulRoutes := route.Group("/v1/hauls", middleware.TokenValidationMiddleware())
	{
		haulRoutes.GET("/statuses", haulHandler.GetHaulStatuses)
		haulRoutes.GET("/activities", haulHandler.GetHaulActivities)
		haulRoutes.GET("/sub-activities", haulHandler.GetHaulSubActivities)

		haulRoutes.GET("/status-board", haulHandler.GetHaulingStatusBoard)
		haulRoutes.GET("/assets/:asset_id/current", haulHandler.GetCurrentAssetHauling)
		haulRoutes.PUT("/assets/:asset_id", haulHandler.UpdateAssetHaulingStatus)

		haulRoutes.GET("/dispatch-locations/:asset_id", haulHandler.GetHaulDispatchLocation)
		haulRoutes.POST("/dispatch-locations/:asset_id", haulHandler.UpsertHaulDispatchLocation)
	}

	driverSessionRoutes := route.Group("/v1/driver-sessions", middleware.TokenValidationMiddleware())
	{
		driverSessionRoutes.GET("", haulHandler.GetDriverLoginSessionList)
	}

	return route
}
