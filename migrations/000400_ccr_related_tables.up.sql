BEGIN;


CREATE TABLE IF NOT EXISTS "ams_HAUL_DISPATCH_LOCATION_TYPES" (
    code VARCHAR(40) PRIMARY KEY,
    label VARCHAR(40) NOT NULL,
    description VARCHAR(50) NOT NULL
);

INSERT INTO "ams_HAUL_DISPATCH_LOCATION_TYPES" (code, "label", description)
VALUES
    ('SHIFT', 'Shift', 'Shift'),
    ('TRIP', 'Trip', 'Trip')
ON CONFLICT (code) DO NOTHING;

CREATE TABLE IF NOT EXISTS "ams_haul_dispatch_locations" (
    asset_id VARCHAR(40) NOT NULL PRIMARY KEY,
    load_location_id VARCHAR(40) REFERENCES "ams_locations" (id),
    dump_location_id VARCHAR(40) REFERENCES "ams_locations" (id),
    dispatch_location_type_code VARCHAR(40) REFERENCES "ams_HAUL_DISPATCH_LOCATION_TYPES" (code),
    client_id VARCHAR(40) NOT NULL,
    created_at TIMESTAMPTZ NOT NULL,
    updated_at TIMESTAMPTZ NOT NULL,
    deleted_at TIMESTAMPTZ,
    updated_by VARCHAR(40) NOT NULL,
    created_by VARCHAR(40) NOT NULL
);

CREATE TABLE IF NOT EXISTS "ams_haul_load_statuses" (
    asset_id VARCHAR(40) NOT NULL PRIMARY KEY,
    is_load BOOLEAN NOT NULL,
    client_id VARCHAR(40) NOT NULL,
    updated_at TIMESTAMPTZ NOT NULL,
    updated_by VARCHAR(40) NOT NULL
);

CREATE OR REPLACE FUNCTION upsert_haul_load_statuses()
RETURNS TRIGGER AS $$
BEGIN
    IF NEW.haul_status_code = 'READY' THEN
        INSERT INTO ams_haul_load_statuses 
        (asset_id, is_load, client_id, updated_at, updated_by)
        VALUES (NEW.asset_id, (
            SELECT is_load
            FROM "ams_HAUL_ACTIVITIES"
            WHERE code = NEW.haul_activity_code
        ), NEW.client_id, NEW.created_at, NEW.created_by)
        ON CONFLICT (asset_id) DO UPDATE
        SET is_load = EXCLUDED.is_load, updated_at = EXCLUDED.updated_at, updated_by = EXCLUDED.updated_by;
    END IF;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

DROP TRIGGER IF EXISTS upsert_haul_load_statuses ON ams_haulings;

CREATE TRIGGER upsert_haul_load_statuses
AFTER INSERT OR UPDATE ON ams_haulings
FOR EACH ROW
EXECUTE FUNCTION upsert_haul_load_statuses();

ALTER TABLE "ams_driver_login_sessions"
ADD COLUMN IF NOT EXISTS "start_vehicle_hm" DOUBLE PRECISION,
ADD COLUMN IF NOT EXISTS "end_vehicle_hm" DOUBLE PRECISION,
ADD COLUMN IF NOT EXISTS "start_vehicle_km" DOUBLE PRECISION,
ADD COLUMN IF NOT EXISTS "end_vehicle_km" DOUBLE PRECISION,
ADD COLUMN IF NOT EXISTS "start_fuel_consumed" DOUBLE PRECISION,
ADD COLUMN IF NOT EXISTS "end_fuel_consumed" DOUBLE PRECISION,
ADD COLUMN IF NOT EXISTS "vehicle_hm" DOUBLE PRECISION GENERATED ALWAYS AS (end_vehicle_hm - start_vehicle_hm) STORED,
ADD COLUMN IF NOT EXISTS "vehicle_km" DOUBLE PRECISION GENERATED ALWAYS AS (end_vehicle_km - start_vehicle_km) STORED,
ADD COLUMN IF NOT EXISTS "fuel_consumed" DOUBLE PRECISION GENERATED ALWAYS AS (end_fuel_consumed - start_fuel_consumed) STORED,
ADD COLUMN IF NOT EXISTS "vehicle_km_per_hm" DOUBLE PRECISION GENERATED ALWAYS AS (vehicle_km / vehicle_hm) STORED,
ADD COLUMN IF NOT EXISTS "fuel_consumed_per_km" DOUBLE PRECISION GENERATED ALWAYS AS (fuel_consumed / vehicle_km) STORED,
ADD COLUMN IF NOT EXISTS "total_net_weight" DECIMAL(10,2),
ADD COLUMN IF NOT EXISTS "total_trip" SMALLINT;


COMMIT;